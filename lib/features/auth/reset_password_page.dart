import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/oneday_logo.dart';
import 'login_page.dart'; // 导入登录页面的组件

/// 重置密码页 - 用户设置新密码
class ResetPasswordPage extends ConsumerStatefulWidget {
  final String? token; // 重置令牌

  const ResetPasswordPage({super.key, this.token});

  @override
  ConsumerState<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends ConsumerState<ResetPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF37352F),
            size: 20,
          ),
          onPressed: () => context.go('/login'),
        ),
        title: Text(
          '重置密码',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(child: _buildBody()),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 40),
          _buildHeader(),
          const SizedBox(height: 48),
          _buildPasswordForm(),
        ],
      ),
    );
  }

  /// 构建页面头部
  Widget _buildHeader() {
    return Column(
      children: [
        // OneDay Logo
        const OneDayLogo(size: 64, enableAnimation: true),
        const SizedBox(height: 24),
        // 标题文字
        Text(
          '设置新密码',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '请输入您的新密码，确保密码安全且易于记忆',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF787774),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建密码表单
  Widget _buildPasswordForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.16),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildPasswordField(),
            const SizedBox(height: 16),
            _buildConfirmPasswordField(),
            const SizedBox(height: 8),
            _buildPasswordRequirements(),
            const SizedBox(height: 24),
            _buildResetButton(),
          ],
        ),
      ),
    );
  }

  /// 构建新密码输入框
  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '新密码',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _passwordController,
          hintText: '请输入新密码',
          obscureText: !_isPasswordVisible,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              color: const Color(0xFF787774),
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
          validator: _validatePassword,
        ),
      ],
    );
  }

  /// 构建确认密码输入框
  Widget _buildConfirmPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '确认密码',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _confirmPasswordController,
          hintText: '请再次输入新密码',
          obscureText: !_isConfirmPasswordVisible,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _isConfirmPasswordVisible
                  ? Icons.visibility_off
                  : Icons.visibility,
              color: const Color(0xFF787774),
            ),
            onPressed: () {
              setState(() {
                _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
              });
            },
          ),
          validator: _validateConfirmPassword,
        ),
      ],
    );
  }

  /// 构建密码要求提示
  Widget _buildPasswordRequirements() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF787774).withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '密码要求：',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 4),
          _buildRequirementItem('至少8位字符'),
          _buildRequirementItem('包含大小写字母'),
          _buildRequirementItem('包含数字'),
          _buildRequirementItem('包含特殊字符（如 !@#\$%^&*）'),
        ],
      ),
    );
  }

  /// 构建要求项目
  Widget _buildRequirementItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 2),
      child: Row(
        children: [
          Icon(Icons.circle, size: 4, color: const Color(0xFF787774)),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(fontSize: 12, color: const Color(0xFF787774)),
          ),
        ],
      ),
    );
  }

  /// 构建重置按钮
  Widget _buildResetButton() {
    return CustomButton(
      text: '重置密码',
      isLoading: _isLoading,
      onPressed: _handleResetPassword,
    );
  }

  /// 验证密码
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入新密码';
    }
    if (value.length < 8) {
      return '密码至少需要8位字符';
    }
    if (!RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
    ).hasMatch(value)) {
      return '密码必须包含大小写字母、数字和特殊字符';
    }
    return null;
  }

  /// 验证确认密码
  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请确认新密码';
    }
    if (value != _passwordController.text) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  /// 处理重置密码
  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟重置密码请求
      await Future.delayed(const Duration(seconds: 2));

      // 重置成功
      if (mounted) {
        _showMessage('密码重置成功，请使用新密码登录');
        await Future.delayed(const Duration(seconds: 1));
        if (mounted) {
          context.go('/login');
        }
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        _showMessage('重置失败，请稍后重试', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示消息
  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFE03E3E)
            : const Color(0xFF2E7EED),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
